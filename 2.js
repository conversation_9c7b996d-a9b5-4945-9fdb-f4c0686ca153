// 向某位女生发送一则表白短信
// name: 女神的姓名
// onFulffiled: 成功后的回调
// onRejected: 失败后的回调
function sendMessage(name) {
    return new Promise((resolve, reject) => {
        // 模拟 发送表白短信
        console.log(
            `邓哥 -> ${name}：最近有谣言说我喜欢你，我要澄清一下，那不是谣言😘`
        );
        console.log(`等待${name}回复......`);
        // 模拟 女神回复需要一段时间
        setTimeout(() => {
            // 模拟 有10%的几率成功
            if (Math.random() <= 0.1) {
                // 成功，调用 onFuffiled，并传递女神的回复
                resolve(`${name} -> 邓哥：我是九，你是三，除了你还是你😘`);
            } else {
                // 失败，调用 onRejected，并传递女神的回复
                reject(`${name} -> 邓哥：你是个好人😜`);
            }
        }, 1000);
    });
}

sendMessage("李建国")
    .catch((reply) => {
        // 失败，继续
        console.log(reply);
        return sendMessage("王富贵");
    })
    .catch((reply) => {
        // 失败，继续
        console.log(reply);
        return sendMessage("周聚财");
    })
    .catch((reply) => {
        // 失败，继续
        console.log(reply);
        return sendMessage("刘人勇");
    })
    .then(
        (reply) => {
            // 成功，结束
            console.log(reply);
            console.log("邓哥终于找到了自己的伴侣");
        },
        (reply) => {
            // 最后一个也失败了
            console.log(reply);
            console.log("邓哥命犯天煞孤星，无伴终老，孤独一生");
        }
    );
